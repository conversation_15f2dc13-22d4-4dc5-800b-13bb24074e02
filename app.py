from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import os
import json
import uuid
from datetime import datetime
from modules.config_manager import ConfigManager
from modules.model_service import ModelService
from modules.character_manager import CharacterManager
from modules.excel_processor import ExcelProcessor
from modules.image_generator import ImageGenerator
from modules.imagehost_manager import ImageHostManager
from modules.txt_to_excel_converter import TxtToExcelConverter
from modules.logger import Logger

app = Flask(__name__)
CORS(app)

config_manager = ConfigManager()
model_service = ModelService(config_manager)
character_manager = CharacterManager(config_manager)
excel_processor = ExcelProcessor()
image_generator = ImageGenerator(model_service, config_manager)
imagehost_manager = ImageHostManager(config_manager)
txt_to_excel_converter = TxtToExcelConverter()
logger = Logger()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/config/models', methods=['GET', 'POST'])
def handle_model_config():
    try:
        if request.method == 'GET':
            return jsonify(config_manager.get_model_config())
        elif request.method == 'POST':
            data = request.json
            config_manager.save_model_config(data)
            return jsonify({"success": True})
    except Exception as e:
        logger.log(f"Error in model config: {str(e)}", "ERROR")
        return jsonify({"error": str(e)}), 500

@app.route('/api/config/characters', methods=['GET', 'POST'])
def handle_character_config():
    try:
        if request.method == 'GET':
            return jsonify(character_manager.get_characters())
        elif request.method == 'POST':
            data = request.json
            if isinstance(data, list):
                # 直接保存角色列表
                character_manager.config_manager.save_characters(data)
            else:
                # 保存单个角色
                character_manager.save_character(data)
            return jsonify({"success": True})
    except Exception as e:
        logger.log(f"Error in character config: {str(e)}", "ERROR")
        return jsonify({"error": str(e)}), 500

@app.route('/api/excel/upload', methods=['POST'])
def upload_excel():
    if 'file' not in request.files:
        return jsonify({"error": "No file uploaded"}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({"error": "No file selected"}), 400
    
    try:
        scenes = excel_processor.process_excel(file)
        logger.log(f"Excel file processed: {file.filename}, {len(scenes)} scenes")
        return jsonify({
            "scenes": scenes,
            "filename": file.filename,
            "scene_count": len(scenes)
        })
    except Exception as e:
        logger.log(f"Error processing Excel: {str(e)}", "ERROR")
        return jsonify({"error": str(e)}), 500

@app.route('/api/generate/single', methods=['POST'])
def generate_single():
    data = request.json
    trace_id = str(uuid.uuid4())
    
    try:
        result = image_generator.generate_single_scene(
            scene_data=data,
            trace_id=trace_id
        )
        return jsonify(result)
    except Exception as e:
        logger.log(f"Error in single generation: {str(e)}", "ERROR", trace_id)
        return jsonify({"error": str(e)}), 500

@app.route('/api/generate/batch', methods=['POST'])
def generate_batch():
    data = request.json
    trace_id = str(uuid.uuid4())
    
    try:
        result = image_generator.generate_batch_scenes(
            scenes_data=data['scenes'],
            trace_id=trace_id
        )
        return jsonify(result)
    except Exception as e:
        logger.log(f"Error in batch generation: {str(e)}", "ERROR", trace_id)
        return jsonify({"error": str(e)}), 500

@app.route('/api/progress/<trace_id>', methods=['GET'])
def get_progress(trace_id):
    try:
        progress_data = image_generator.get_progress(trace_id)
        return jsonify(progress_data)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "Endpoint not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    logger.log(f"Internal server error: {str(error)}", "ERROR")
    return jsonify({"error": "Internal server error"}), 500

@app.route('/api/logs', methods=['GET'])
def get_logs():
    return jsonify(logger.get_recent_logs())

@app.route('/api/config/save-settings', methods=['GET', 'POST'])
def save_settings():
    if request.method == 'GET':
        # 获取当前保存设置
        try:
            config = config_manager.get_model_config()
            settings = config.get("settings", {})
            return jsonify({
                "prevent_overwrite": settings.get("prevent_overwrite", True),
                "add_timestamp": settings.get("add_timestamp", True),
                "filename_format": settings.get("filename_format", "timestamp")
            })
        except Exception as e:
            return jsonify({"error": str(e)}), 500

    elif request.method == 'POST':
        # 更新保存设置
        try:
            data = request.json
            config = config_manager.get_model_config()
            settings = config.get("settings", {})

            # 更新设置
            if "prevent_overwrite" in data:
                settings["prevent_overwrite"] = bool(data["prevent_overwrite"])
            if "add_timestamp" in data:
                settings["add_timestamp"] = bool(data["add_timestamp"])
            if "filename_format" in data:
                settings["filename_format"] = data["filename_format"]

            # 保存配置
            config_manager.save_model_config({"settings": settings})

            return jsonify({"success": True, "message": "保存设置已更新"})
        except Exception as e:
            return jsonify({"error": str(e)}), 500

@app.route('/api/select-directory', methods=['POST'])
def select_directory():
    """使用Windows文件夹选择对话框"""
    try:
        import tkinter as tk
        from tkinter import filedialog

        # 创建隐藏的根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        root.attributes('-topmost', True)  # 置顶显示

        # 获取当前保存目录作为初始目录
        current_dir = request.json.get('current_directory', '')
        initial_dir = current_dir if current_dir and os.path.exists(current_dir) else os.getcwd()

        # 打开文件夹选择对话框
        selected_directory = filedialog.askdirectory(
            title="选择保存目录",
            initialdir=initial_dir
        )

        root.destroy()  # 销毁窗口

        if selected_directory:
            return jsonify({
                "success": True,
                "directory": selected_directory
            })
        else:
            return jsonify({
                "success": False,
                "message": "未选择目录"
            })

    except ImportError:
        return jsonify({
            "success": False,
            "error": "tkinter模块不可用，无法使用文件夹选择功能"
        }), 500
    except Exception as e:
        logger.log(f"Directory selection error: {str(e)}", "ERROR")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/excel/export', methods=['POST'])
def export_excel():
    try:
        # 确保正确处理JSON数据
        if not request.is_json:
            raise Exception("Request must be JSON")

        data = request.get_json(force=True)
        if not data:
            raise Exception("No JSON data received")

        scenes = data.get('scenes', [])
        filename = data.get('filename', 'exported_scenes.xlsx')

        logger.log(f"Exporting Excel with {len(scenes)} scenes, filename: {filename}")

        if not scenes:
            raise Exception("No scenes data to export")

        # 生成Excel文件
        excel_file_path = excel_processor.export_scenes_to_excel(scenes, filename)

        logger.log(f"Excel exported successfully: {excel_file_path}")

        # 检查文件是否存在和大小
        if os.path.exists(excel_file_path):
            file_size = os.path.getsize(excel_file_path)
            logger.log(f"Excel file size: {file_size} bytes")
            if file_size == 0:
                raise Exception("Generated Excel file is empty")
        else:
            raise Exception("Excel file was not created")

        return send_file(excel_file_path, as_attachment=True, download_name=filename)

    except Exception as e:
        logger.log(f"Error exporting Excel: {str(e)}", "ERROR")
        return jsonify({"error": str(e)}), 500

@app.route('/api/config/export', methods=['GET'])
def export_config():
    try:
        config_data = config_manager.export_config()
        return jsonify(config_data)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/config/import', methods=['POST'])
def import_config():
    try:
        data = request.json
        config_manager.import_config(data)
        return jsonify({"success": True})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/imagehost/config', methods=['GET', 'POST'])
def handle_imagehost_config():
    if request.method == 'GET':
        try:
            config = imagehost_manager.get_imagehost_config()
            return jsonify(config)
        except Exception as e:
            logger.log(f"Error getting imagehost config: {str(e)}", "ERROR")
            return jsonify({"error": str(e)}), 500

    elif request.method == 'POST':
        try:
            data = request.json
            success = imagehost_manager.save_imagehost_config(data)
            if success:
                return jsonify({"success": True, "message": "图床配置保存成功"})
            else:
                return jsonify({"success": False, "error": "保存失败"}), 500
        except Exception as e:
            logger.log(f"Error saving imagehost config: {str(e)}", "ERROR")
            return jsonify({"error": str(e)}), 500

@app.route('/api/imagehost/test', methods=['POST'])
def test_imagehost_connection():
    try:
        data = request.json
        result = imagehost_manager.test_connection(data)
        return jsonify(result)
    except Exception as e:
        logger.log(f"Error testing imagehost connection: {str(e)}", "ERROR")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/imagehost/upload', methods=['POST'])
def upload_image():
    try:
        if 'file' not in request.files:
            return jsonify({"success": False, "error": "没有选择文件"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"success": False, "error": "没有选择文件"}), 400

        # 检查文件类型
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif'}
        if not ('.' in file.filename and
                file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({"success": False, "error": "不支持的文件格式"}), 400

        # 读取文件内容
        file_content = file.read()

        # 上传图片
        result = imagehost_manager.upload_image(file_content, file.filename)

        return jsonify(result)

    except Exception as e:
        logger.log(f"Error uploading image: {str(e)}", "ERROR")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/imagehost/history', methods=['GET'])
def get_upload_history():
    try:
        history = imagehost_manager.get_upload_history()
        return jsonify({"success": True, "history": history})
    except Exception as e:
        logger.log(f"Error getting upload history: {str(e)}", "ERROR")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/imagehost/history/<item_id>', methods=['DELETE'])
def delete_history_item(item_id):
    try:
        success = imagehost_manager.delete_history_item(item_id)
        if success:
            return jsonify({"success": True, "message": "历史记录已删除"})
        else:
            return jsonify({"success": False, "error": "记录不存在"}), 404
    except Exception as e:
        logger.log(f"Error deleting history item: {str(e)}", "ERROR")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/imagehost/history/clear', methods=['POST'])
def clear_upload_history():
    try:
        success = imagehost_manager.clear_history()
        if success:
            return jsonify({"success": True, "message": "历史记录已清空"})
        else:
            return jsonify({"success": False, "error": "清空失败"}), 500
    except Exception as e:
        logger.log(f"Error clearing upload history: {str(e)}", "ERROR")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/excel/convert-txt', methods=['POST'])
def convert_txt_to_excel():
    try:
        data = request.json
        file_content = data.get('file_content')
        original_filename = data.get('original_filename')
        save_directory = data.get('save_directory')

        if not file_content:
            return jsonify({"success": False, "error": "文件内容不能为空"}), 400

        if not original_filename:
            return jsonify({"success": False, "error": "文件名不能为空"}), 400

        if not save_directory:
            return jsonify({"success": False, "error": "保存目录不能为空"}), 400

        # 转换TXT到Excel
        result = txt_to_excel_converter.convert_txt_to_excel(
            file_content, original_filename, save_directory
        )

        return jsonify(result)

    except Exception as e:
        logger.log(f"Error converting TXT to Excel: {str(e)}", "ERROR")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/excel/validate-txt', methods=['POST'])
def validate_txt_content():
    try:
        data = request.json
        file_content = data.get('file_content')

        if not file_content:
            return jsonify({"valid": False, "error": "文件内容不能为空"}), 400

        # 验证TXT内容
        result = txt_to_excel_converter.validate_txt_content(file_content)

        return jsonify(result)

    except Exception as e:
        logger.log(f"Error validating TXT content: {str(e)}", "ERROR")
        return jsonify({"valid": False, "error": str(e)}), 500

@app.route('/api/excel/download', methods=['POST'])
def download_excel_file():
    try:
        data = request.json
        file_path = data.get('file_path')

        if not file_path or not os.path.exists(file_path):
            return jsonify({"error": "文件不存在"}), 404

        # 获取文件名
        filename = os.path.basename(file_path)

        return send_file(file_path, as_attachment=True, download_name=filename)

    except Exception as e:
        logger.log(f"Error downloading Excel file: {str(e)}", "ERROR")
        return jsonify({"error": str(e)}), 500

@app.route('/api/open-directory', methods=['POST'])
def open_directory():
    try:
        data = request.json
        directory = data.get('directory')

        if not directory or not os.path.exists(directory):
            return jsonify({"success": False, "error": "目录不存在"}), 400

        # 根据操作系统打开目录
        import subprocess
        import platform

        system = platform.system()
        if system == "Windows":
            # 在Windows中，使用os.startfile是最可靠的方法
            os.startfile(directory)
        elif system == "Darwin":  # macOS
            subprocess.run(['open', directory])
        else:  # Linux
            subprocess.run(['xdg-open', directory])

        return jsonify({"success": True, "message": "目录已打开"})

    except Exception as e:
        logger.log(f"Error opening directory: {str(e)}", "ERROR")
        return jsonify({"success": False, "error": str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)